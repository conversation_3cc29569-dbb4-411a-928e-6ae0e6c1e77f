<?php

namespace Modules\Bank\Integrations\Klb;

use App\Settings\BankSettings;
use JsonException;
use Modules\Bank\Integrations\Klb\Requests\AccessTokenRequest;
use Saloon\Exceptions\Request\FatalRequestException;
use Saloon\Exceptions\Request\RequestException;
use Saloon\Http\Auth\TokenAuthenticator;
use Saloon\Http\Connector;
use Saloon\Http\Response;
use Saloon\Traits\Plugins\AcceptsJson;

class KlbConnector extends Connector
{
    use AcceptsJson;

    public ?int $tries = 2;

    // Checking for requesting class to avoid infinite looping for access token request class
    private ?string $currentRequestClass = null;

    protected BankSettings $settings;

    public function __construct()
    {
        $this->settings = app(BankSettings::class);
    }

    public function resolveBaseUrl(): string
    {
        return config('bank.supported_banks.klb.api_config.base_url', 'https://api.kienlongbank.co/pay');
    }

    public function hasRequestFailed(Response $response): ?bool
    {
        // Check if the response is a 401 Unauthorized
        if ($response->status() === 401) {
            $this->settings->clearTokens('klb');
        }

        return $response->status() >= 500;
    }

    /**
     * @throws FatalRequestException
     * @throws RequestException
     * @throws JsonException
     */
    public function getAccessToken(): string
    {
        $token = $this->settings->getAccessToken('klb');

        if (!$token || $this->settings->isTokenExpired('klb')) {
            $this->currentRequestClass = AccessTokenRequest::class;
            $response = $this->send(new AccessTokenRequest);
            $this->currentRequestClass = null;

            if ($response->failed()) {
                throw new \Exception('Không thể gửi yêu cầu lấy access token với ngân hàng KienLongBank, vui lòng liên hệ với bộ phận hỗ trợ.');
            }

            $responseData = $response->array();
            $token = $responseData['access_token'] ?? null;

            if (!$token) {
                throw new \Exception('No access token received from KLB API');
            }

            $expiresIn = $responseData['expires_in'] ?? 3600;
            $expiresAt = now()->addSeconds($expiresIn - 60)->toDateTimeString();

            $this->settings->setAccessToken('klb', $token, $expiresAt);
        }

        return $token;
    }

    /**
     * @throws FatalRequestException
     * @throws RequestException
     * @throws JsonException
     */
    protected function defaultAuth(): TokenAuthenticator
    {
        return new TokenAuthenticator($this->currentRequestClass !== AccessTokenRequest::class ? $this->getAccessToken() : '');
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'x-api-client' => config('bank.supported_banks.klb.api_config.client_id'),
        ];
    }

    /**
     * Get client configuration for KLB API
     */
    public function getClientId(): string
    {
        return config('bank.supported_banks.klb.api_config.client_id', '');
    }

    /**
     * Get secret key for KLB API
     */
    public function getSecretKey(): string
    {
        return config('bank.supported_banks.klb.api_config.secret_key', '');
    }

    /**
     * Get encrypt key for KLB API
     */
    public function getEncryptKey(): string
    {
        return config('bank.supported_banks.klb.api_config.encrypt_key', '');
    }
}
